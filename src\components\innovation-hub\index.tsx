'use client';

import React, { useState, useEffect } from 'react';
import { Lightbulb, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { PERMISSIONS, hasPermission } from '@/lib/types/permissions';
import {
  GetIdeas,
  GetIdeaStats,
  toggleIdeaLike,
  updateIdeaStatus,
  addIdeaComment,
  deleteIdea,
  type Idea,
  type IdeaStatus,
} from '@/api/innovation-hub/data';
import { IdeaCard } from './components/idea-card';
import { CommentModal } from './components/comment-modal';
import { SearchFilter } from './components/search-filter';
import { LoadingState } from './components/loading-state';
import { ErrorState } from './components/error-state';
import { StatsCards } from './components/stats-cards';
import { EmptyState } from './components/empty-state';
import NewIdea from './components/new-idea';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import dayjs from 'dayjs';


export default function InnovationHubContent() {
  const [activeTab, setActiveTab] = useState<'all' | 'my'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<IdeaStatus | 'all'>('all');
  const [sortBy, setSortBy] = useState<'createdAt' | 'likes' | 'title'>(
    'createdAt'
  );
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isCommentModalOpen, setCommentModalOpen] = useState(false);
  const [selectedIdeaForComments, setSelectedIdeaForComments] =
    useState<Idea | null>(null);
  const [openNewIdeaModal, setOpenNewIdeaModal] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

    const {
      searchTerm,
      debouncedSearchTerm,
      handleSearchChange,
      currentPage,
      pageSize,
      handlePageChange,
      queryParam,
      setQueryParam,
    } = useSearchAndPagination({ initialPageSize: 10 });

  const canEdit = hasPermission(PERMISSIONS.HUB_EDIT);

    useEffect(() => {
      let params = [];
  

      if (statusFilter && statusFilter !== 'all') {
        params.push(`status=${statusFilter}`);
      }
  
      if (debouncedSearchTerm) {
        params.push(`search=${debouncedSearchTerm}`);
      }
  
      if (startDate) {
        const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
        params.push(`startDate=${formattedStartDate}`);
      }
  
      if (endDate) {
        const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
        params.push(`endDate=${formattedEndDate}`);
      }
  
      setQueryParam(params.join('&'));
    }, [
      debouncedSearchTerm,
      statusFilter,
      startDate,
      endDate,
      setQueryParam,
    ]);


  const { ideas, isLoading, error, mutate } =
    GetIdeas(`?page=${currentPage}&limit=${pageSize}&${queryParam}`);

    

  const handleLike = async (id: string) => {
    try {
      await toggleIdeaLike(id);
      mutate();
    } catch (error) {
      console.error('Error liking idea:', error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteIdea(id);
      mutate();
    } catch (error) {
      console.error('Error deleting idea:', error);
    }
  };

  const handleStatusUpdate = async (id: string, newStatus: IdeaStatus) => {
    try {
      await updateIdeaStatus(id, newStatus);
      mutate();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const handleOpenCommentModal = (idea: Idea) => {
    setSelectedIdeaForComments(idea);
    setCommentModalOpen(true);
  };

  const handleCommentSubmit = async (commentText: string) => {
    if (!selectedIdeaForComments) return;

    try {
      await addIdeaComment(selectedIdeaForComments.id, commentText);
      setCommentModalOpen(false);
      mutate();
    } catch (error) {
      console.error('Error adding comment:', error);
    }
  };


  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Lightbulb className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Innovation Hub
              </h1>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Explore, share, and collaborate on cutting-edge ideas.
                <br />
                The innovation hub is a powerful initiative that can help
                transform our healthcare delivery, improve patient outcomes,
                reduce costs, and enhance staff experience.
              </p>
            </div>
          </div>
        </div>
        <div className=" gap-8 ">
          {/* Ideas Feed */}
          <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
                  <Button
                    variant={activeTab === 'all' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => {
                      setActiveTab('all');
                      setCurrentPage(1);
                    }}
                    className="px-4 py-2"
                  >
                    All Ideas
                  </Button>
                  <Button
                    variant={activeTab === 'my' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => {
                      setActiveTab('my');
                      setCurrentPage(1);
                    }}
                    className="px-4 py-2"
                  >
                    My Ideas
                  </Button>
                </div>
                <Button
                  onClick={() => setOpenNewIdeaModal(true)}
                  className="cursor-pointer"
                >
                  <Plus className="w-3.5 h-3.5" /> New Idea
                </Button>
              </div>

              {!statsLoading && <StatsCards />}

              <SearchFilter
                searchQuery={searchQuery}
                onSearchChange={handleSearchChange}
                statusFilter={statusFilter}
                onStatusChange={handleStatusChange}
                sortBy={sortBy}
                onSortChange={handleSortChange}
                sortOrder={sortOrder}
                onSortOrderChange={handleSortOrderChange}
              />

              {error ? (
                <ErrorState error={error} onRetry={() => mutate()} />
              ) : isLoading ? (
                <LoadingState />
              ) : ideas.length === 0 ? (
                <EmptyState
                  title={
                    searchQuery || statusFilter !== 'all'
                      ? 'No ideas match your search'
                      : 'No ideas yet'
                  }
                  description={
                    searchQuery || statusFilter !== 'all'
                      ? 'Try adjusting your search criteria or filters.'
                      : 'Be the first to share your innovative idea and inspire others!'
                  }
                  actionText="Clear Filters"
                  onAction={
                    searchQuery || statusFilter !== 'all'
                      ? () => {
                          setSearchQuery('');
                          setStatusFilter('all');
                          setCurrentPage(1);
                        }
                      : undefined
                  }
                />
              ) : (
                <>
                  {ideas.map((idea: Idea) => (
                    <IdeaCard
                      key={idea.id}
                      idea={idea}
                      onLike={handleLike}
                      canEdit={canEdit}
                      onDelete={handleDelete}
                      onStatusUpdate={handleStatusUpdate}
                      onCommentClick={handleOpenCommentModal}
                    />
                  ))}

                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    total={total}
                    limit={10}
                  />
                </>
              )}
            </div>
          </div>
        </div>

        <CommentModal
          idea={selectedIdeaForComments}
          open={isCommentModalOpen}
          onOpenChange={setCommentModalOpen}
          onCommentSubmit={handleCommentSubmit}
        />
      </div>
      <NewIdea
        open={openNewIdeaModal}
        setOpen={setOpenNewIdeaModal}
        mutate={mutate}
      />
    </>
  );
}
