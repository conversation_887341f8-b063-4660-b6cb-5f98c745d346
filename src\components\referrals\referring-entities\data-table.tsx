'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis, Search, Star, Building2 } from 'lucide-react';
import { GetAllReferringEntites } from '@/api/referral/data';
import { StatusBadge } from '@/components/common/status-badge';

import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';

const ReferringEntity = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [statusFilter, setRatingFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Update query parameters when search term, rating filter, or date range changes
  useEffect(() => {
    let params = [];

    if (statusFilter && statusFilter !== 'all') {
      params.push(`status=${statusFilter}`);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, statusFilter, startDate, endDate, setQueryParam]);

  const { referrers, referrerLoading, mutate } = GetAllReferringEntites(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const referrerData = referrers?.data?.referers;
  const totalPages = referrers?.data?.totalPages ?? 0;

  const handleEventFromModal = (feedback: any) => {
    setDetail(feedback);
    setOpen(true);
  };

  const handleRatingChange = (value: string) => {
    setRatingFilter(value);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  // Rating options for the dropdown
  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'PENDING_REVIEW', label: 'Pending Review' },
    { value: 'ACCEPTED', label: 'Accepted' },
    { value: 'IN_PROGRESS', label: 'In Progress' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'REJECTED', label: 'Rejected' },
    { value: 'CANCELLED', label: 'Cancelled' },
  ];

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="flex flex-wrap gap-3 items-center w-full md:w-auto mb-4 md:mb-0">
            <Select value={statusFilter} onValueChange={handleRatingChange}>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
          </div>
          <div className="relative w-full md:w-64">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-gray-500" />
            </div>
            <Input
              type="text"
              placeholder="Search referrals..."
              className="pl-10 pr-4 py-2 w-full"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Type</th>
              <th className="table-style">Last login</th>
              <th className="table-style">Name</th>
              <th className="table-style">Email</th>
              <th className="table-style">Phone Number</th>
              <th className="table-style">Validated?</th>
              <th className="table-style">Activated?</th>
              <th className="table-style">Status</th>
              <th className="table-style">Details</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {referrerLoading ? (
              <tr>
                <td colSpan={10} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading referring entities...</span>
                  </div>
                </td>
              </tr>
            ) : referrerData && referrerData.length > 0 ? (
              referrerData.map((dat: any, index: any) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={dat.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dat.entityType === 'ORGANIZATION'
                      ? 'Organization'
                      : 'Individual '}
                  </td>
                  <td className="table-style">
                    {dat.lastLogin
                      ? dayjs(dat.lastLogin).format('MMMM D, YYYY')
                      : '--'}
                  </td>
                  <td className="table-style">{dat.name}</td>
                  <td className="table-style">{dat.email}</td>
                  <td className="table-style">{dat.phoneNumber}</td>
                  <td className="table-style">
                    <span
                      className={
                        dat.isEmailValidated
                          ? 'text-green-600 font-medium'
                          : 'text-gray-500 font-medium'
                      }
                    >
                      {dat.isEmailValidated ? 'Validated' : 'Unvalidated'}
                    </span>
                  </td>
                  <td className="table-style">
                    <span
                      className={
                        dat.activated
                          ? 'text-green-600 font-medium'
                          : 'text-gray-500 font-medium'
                      }
                    >
                      {dat.activated ? 'Activated' : 'Unactivated'}
                    </span>
                  </td>
                  <td className="table-style">
                    {dat.isActive
                      ? StatusBadge({ status: 'active' })
                      : StatusBadge({ status: 'inactive' })}
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(dat)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={10} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <Building2 className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">
                      No referring entities found
                    </p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Referring entities will appear here once they are created'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="referring entities"
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          totalCount={referrers?.data?.totalCount}
        />
      ) : (
        ''
      )}
      {open && (
        <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      )}
    </>
  );
};

export default ReferringEntity;
