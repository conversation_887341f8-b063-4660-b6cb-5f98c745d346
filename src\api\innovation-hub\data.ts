import useSWR from 'swr';
import { myApi } from '../fetcher';
import { toast } from 'sonner';

// Types
export interface Idea {
  id: string;
  title: string;
  description: string;
  author: {
    id: string;
    fullName: string;
  };
  createdAt: string;
  updatedAt: string;
  likes: number;
  comments: IdeaComment[];
  status: IdeaStatus;
  isLikedByUser?: boolean;
}

export interface IdeaComment {
  id: string;
  ideaId: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  text: string;
  createdAt: string;
  updatedAt: string;
}

export type IdeaStatus =
  | 'New'
  | 'Under Review'
  | 'Approved'
  | 'Rejected'
  | 'Implemented';

export interface IdeaFormValues {
  title: string;
  description: string;
  category?: string;
  tags?: string[];
}

export interface IdeasResponse {
  ideas: Idea[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SearchParams {
  query?: string;
  status?: IdeaStatus;
  category?: string;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'likes' | 'title';
  sortOrder?: 'asc' | 'desc';
  myIdeas?: boolean;
}

export const GetIdeaCategories = () => {
  const { data, isLoading, error, mutate } = useSWR(
    '/innovation/idea-categories'
  );

  return {
    ideasCat: data?.data || [],
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};

export const GetIdeaTags = () => {
  const { data, isLoading, error, mutate } = useSWR('/innovation/idea-tags');

  return {
    ideasTags: data?.data || [],
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};


export const GetIdeas = (params: string) => {
  const qs = new URLSearchParams();

  const { data, error, isLoading, mutate } = useSWR(
    `/innovation/idea/list?${qs.toString()}`
  );

  return {
    ideas: data?.data,
    isLoading: isLoading,
    error: error,
    mutate: mutate,
  };
};


// Like/Unlike an idea
export const toggleIdeaLike = async (ideaId: string) => {
  try {
    const response = await myApi.post(`/innovation-hub/ideas/${ideaId}/like`);
    return response.data;
  } catch (error) {
    console.error('Error toggling idea like:', error);
    throw error;
  }
};

// Update idea status (admin only)
export const updateIdeaStatus = async (ideaId: string, status: IdeaStatus) => {
  try {
    const response = await myApi.put(`/innovation-hub/ideas/${ideaId}/status`, {
      status,
    });
    toast.success(`Idea status updated to "${status}"`);
    return response.data;
  } catch (error) {
    console.error('Error updating idea status:', error);
    throw error;
  }
};

// Add comment to an idea
export const addIdeaComment = async (ideaId: string, text: string) => {
  try {
    const response = await myApi.post(
      `/innovation-hub/ideas/${ideaId}/comments`,
      { text }
    );
    toast.success('Comment added successfully');
    return response.data;
  } catch (error) {
    console.error('Error adding comment:', error);
    throw error;
  }
};

// Delete an idea
export const deleteIdea = async (ideaId: string) => {
  try {
    const response = await myApi.delete(`/innovation-hub/ideas/${ideaId}`);
    toast.success('Idea deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting idea:', error);
    throw error;
  }
};

// Get innovation hub statistics
export const GetIdeaStats = () => {
  const { data, error, isLoading, mutate } = useSWR('/innovation-hub/stats');

  return {
    stats: data?.data || {
      totalIdeas: 0,
      approvedIdeas: 0,
      underReviewIdeas: 0,
      implementedIdeas: 0,
      rejectedIdeas: 0,
      newIdeas: 0,
    },
    isLoading,
    error,
    mutate,
  };
};
